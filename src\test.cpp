#include <iostream>
#include <Windows.h>
#include "../include/DbConnect.h"
using namespace std;
using namespace sql;

int main() {
	try {
		DbConnect db1;
		string sql = "select * from info_user";
		shared_ptr<ResultSet> rs = db1.GetRecords(sql);
		
		// 需要先调用next()移动到第一行记录
		if (rs->next()) {
			string userName = rs->getString("user_name");
			cout << "用户名: " << userName << endl;
		}
		return 0;
	}
	catch (exception& e) {
		cerr << "发生异常: " << e.what() << endl;
		return 1;
	}
	return 0;
}