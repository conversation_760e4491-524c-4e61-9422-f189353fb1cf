﻿#include "../include/DbConnect.h"

DbConnect::DbConnect() {
	this->host = "tcp://localhost:3306";  // 主机+端口
	this->user = "root";                  // 用户名
	this->password = "123456";			  // 密码
	this->database = "public";            // 要连接的数据库
	this->db = nullptr;                 // 初始化连接指针

	try {
		// 1. 获取MySQL驱动
		Driver* driver = get_driver_instance();
		if (driver == NULL) {
			cout << "获取MySQL驱动失败?" << endl;
		}

		// 2. 建立数据库连接
		db = shared_ptr<Connection>(driver->connect(host, user, password));
		if (!db) {
			cerr << "数据库连接失败！" << endl;
		}

		// 3. 选择数据库
		db->setSchema(database);
	}
	catch (const SQLException& e) {
		cout << "连接失败:" << e.what() << endl;
	}
}

DbConnect::~DbConnect() {
    // shared_ptr会自动释放连接资源
}

shared_ptr<ResultSet> DbConnect::GetRecords(string sql){
	shared_ptr<Statement> stmt(db->createStatement());
	return shared_ptr<ResultSet>(stmt->executeQuery(sql));
}
shared_ptr<ResultSet> DbConnect::Get1Record(string sql){
  shared_ptr<Statement> stmt(db->createStatement());
	return shared_ptr<ResultSet>(stmt->executeQuery(sql));
}